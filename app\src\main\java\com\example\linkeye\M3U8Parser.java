package com.example.linkeye;

import android.content.Context;
import android.content.res.Resources;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class M3U8Parser {
    private static final Pattern EXTINF_PATTERN = Pattern.compile(
        "#EXTINF:-1\\s+(.*)\\s*,(.*)$"
    );
    
    private static final Pattern TVG_ID_PATTERN = Pattern.compile("tvg-id=\"([^\"]+)\"");
    private static final Pattern TVG_LOGO_PATTERN = Pattern.compile("tvg-logo=\"([^\"]+)\"");
    private static final Pattern GROUP_TITLE_PATTERN = Pattern.compile("group-title=\"([^\"]+)\"");

    public static List<TvGroup> parseM3U8(Context context) {
        Map<String, TvGroup> groupMap = new HashMap<>();
        List<TvGroup> groups = new ArrayList<>();
        
        try {
            Resources res = context.getResources();
            int id = res.getIdentifier("tv_channels", "raw", context.getPackageName());
            if (id == 0) return groups;
            
            InputStream is = res.openRawResource(id);
            BufferedReader br = new BufferedReader(new InputStreamReader(is, "UTF-8"));
            
            String line;
            String currentChannelInfo = null;
            String currentChannelName = null;
            String currentTvgId = null;
            String currentTvgLogo = null;
            String currentGroupTitle = "未分组";
            
            while ((line = br.readLine()) != null) {
                line = line.trim();
                
                if (line.startsWith("#EXTINF:")) {
                    // Parse channel info line
                    Matcher matcher = EXTINF_PATTERN.matcher(line);
                    if (matcher.find()) {
                        currentChannelInfo = matcher.group(1);
                        currentChannelName = matcher.group(2);
                        
                        // Extract tvg-id
                        Matcher tvgIdMatcher = TVG_ID_PATTERN.matcher(currentChannelInfo);
                        currentTvgId = tvgIdMatcher.find() ? tvgIdMatcher.group(1) : "";
                        
                        // Extract tvg-logo
                        Matcher tvgLogoMatcher = TVG_LOGO_PATTERN.matcher(currentChannelInfo);
                        currentTvgLogo = tvgLogoMatcher.find() ? tvgLogoMatcher.group(1) : "";
                        
                        // Extract group-title
                        Matcher groupMatcher = GROUP_TITLE_PATTERN.matcher(currentChannelInfo);
                        currentGroupTitle = groupMatcher.find() ? groupMatcher.group(1) : "未分组";
                    }
                } else if (!line.startsWith("#") && !line.isEmpty() && currentChannelName != null) {
                    // This is a URL line, create channel
                    List<String> sources = parseChannelSources(line);
                    if (!sources.isEmpty()) {
                        TvChannel channel = new TvChannel(
                            currentChannelName,
                            currentTvgId,
                            currentTvgLogo,
                            currentGroupTitle,
                            sources,
                            false // Default not muted for TV channels
                        );
                        
                        // Get or create group
                        TvGroup group = groupMap.get(currentGroupTitle);
                        if (group == null) {
                            group = new TvGroup(currentGroupTitle);
                            groupMap.put(currentGroupTitle, group);
                            groups.add(group);
                        }
                        
                        group.addChannel(channel);
                    }
                    
                    // Reset for next channel
                    currentChannelInfo = null;
                    currentChannelName = null;
                    currentTvgId = null;
                    currentTvgLogo = null;
                    currentGroupTitle = "未分组";
                }
            }
            
            br.close();
        } catch (Exception e) {
            // Log error but don't crash
            e.printStackTrace();
        }
        
        return groups;
    }
    
    private static List<String> parseChannelSources(String sourceLine) {
        List<String> sources = new ArrayList<>();
        
        // Split by $ to get multiple sources
        String[] parts = sourceLine.split("\\$");
        for (String part : parts) {
            String url = part.trim();
            if (!url.isEmpty() && (url.startsWith("http") || url.startsWith("rtsp") || url.startsWith("rtmp"))) {
                sources.add(url);
            }
        }
        
        return sources;
    }
}

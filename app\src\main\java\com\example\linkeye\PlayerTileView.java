package com.example.linkeye;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.widget.FrameLayout;
import android.widget.TextView;
import android.widget.ImageView;
import android.view.View;
import android.view.KeyEvent;
import android.os.Handler;
import android.os.Looper;

import tv.danmaku.ijk.media.player.IMediaPlayer;
import tv.danmaku.ijk.media.player.IjkMediaPlayer;

import java.io.IOException;

public class PlayerTileView extends FrameLayout implements SurfaceHolder.Callback {
    private SurfaceView surfaceView;
    private SurfaceHolder surfaceHolder;
    private TextView titleView;
    private ImageView muteBadge;
    private View focusBorder;
    private View loadingIndicator;

    private IjkMediaPlayer player;
    private CameraInfo camera;
    private boolean muted;

    private Handler focusHandler = new Handler(Looper.getMainLooper());
    private Runnable focusTimeoutRunnable;

    // Retry mechanism for failed connections
    private int retryCount = 0;
    private static final int MAX_RETRY_COUNT = 3;
    private static final int RETRY_DELAY_MS = 2000; // 2 seconds

    // TV channel source switching
    private boolean isRetryingTvSources = false;

    public PlayerTileView(Context context) {
        super(context);
        init(context);
    }

    public PlayerTileView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public PlayerTileView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        LayoutInflater.from(context).inflate(R.layout.player_tile, this, true);
        surfaceView = (SurfaceView) findViewById(R.id.surface_view);
        surfaceHolder = surfaceView.getHolder();
        surfaceHolder.addCallback(this);
        titleView = (TextView) findViewById(R.id.title);
        muteBadge = (ImageView) findViewById(R.id.mute_badge);
        focusBorder = findViewById(R.id.focus_border);
        loadingIndicator = findViewById(R.id.loading_indicator);

        setFocusable(true);
        setFocusableInTouchMode(false); // Important for TV navigation
        setClickable(true);
        setOnFocusChangeListener((v, hasFocus) -> {
            if (focusBorder != null) {
                if (hasFocus) {
                    // Create a drawable with border only
                    android.graphics.drawable.GradientDrawable border = new android.graphics.drawable.GradientDrawable();
                    border.setColor(android.graphics.Color.TRANSPARENT);
                    border.setStroke(6, 0xFF00BFFF); // 6px blue border
                    focusBorder.setBackground(border);

                    // Start auto-unfocus timer
                    startFocusTimeout();
                } else {
                    focusBorder.setBackground(null);
                    // Cancel auto-unfocus timer
                    cancelFocusTimeout();
                }
            }
        });
    }

    public void assignCamera(CameraInfo info) {
        this.camera = info;
        this.retryCount = 0; // Reset retry count when assigning new camera
        this.isRetryingTvSources = false; // Reset TV source retry flag

        // Reset TV channel source index if it's a TV channel
        if (info instanceof TvChannel) {
            ((TvChannel) info).resetSourceIndex();
        }

        updateTitle();
        if (this.camera == null) {
            release();
            // Set surface to black when no camera assigned
            if (surfaceView != null) {
                surfaceView.setBackgroundColor(0xFF000000); // Black background
            }
            return;
        }
        // Clear black background when camera is assigned
        if (surfaceView != null) {
            surfaceView.setBackgroundColor(0x00000000); // Transparent background
        }
        restartIfReady();
    }

    public CameraInfo getAssignedCamera() {
        return camera;
    }

    public void setMuted(boolean muted) {
        this.muted = muted;
        applyMute();
    }

    public boolean isMuted() {
        return muted;
    }

    private void updateTitle() {
        if (titleView != null) {
            String title = "空";
            if (camera != null) {
                title = camera.name;
                // Show source info for TV channels with multiple sources
                if (camera instanceof TvChannel) {
                    TvChannel tvChannel = (TvChannel) camera;
                    if (tvChannel.getSourceCount() > 1) {
                        title += " (" + (tvChannel.getCurrentSourceIndex() + 1) + "/" + tvChannel.getSourceCount() + ")";
                    }
                }
            }
            titleView.setText(title);
        }
        if (muteBadge != null) {
            muteBadge.setVisibility(muted ? VISIBLE : GONE);
        }
    }

    private void applyMute() {
        if (player != null) {
            float vol = muted ? 0f : 1f;
            player.setVolume(vol, vol);
        }
        if (muteBadge != null) {
            muteBadge.setVisibility(muted ? VISIBLE : GONE);
        }
    }

    private void restartIfReady() {
        if (surfaceHolder != null && surfaceHolder.getSurface().isValid()) {
            prepareAndStart();
        }
    }

    private void prepareAndStart() {
        release();
        if (camera == null) {
            hideLoadingIndicator();
            return;
        }

        String url = getCurrentUrl();
        if (url == null || url.isEmpty()) {
            hideLoadingIndicator();
            return;
        }

        // Show loading indicator when starting to connect
        showLoadingIndicator();

        player = new IjkMediaPlayer();
        // Add more robust options for better connection stability
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "rtsp_transport", "tcp");
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "rtsp_flags", "prefer_tcp");
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "timeout", 10000000); // 10 seconds timeout
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "start-on-prepared", 1);
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "reconnect", 1);
        final int SDL_FCC_YV12 = 0x32315659;
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "overlay-format", SDL_FCC_YV12);
        player.setDisplay(surfaceHolder);

        try {
            player.setDataSource(url);
            player.setOnPreparedListener(new IjkMediaPlayer.OnPreparedListener() {
                @Override
                public void onPrepared(IMediaPlayer mp) {
                    retryCount = 0; // Reset retry count on successful preparation
                    isRetryingTvSources = false; // Reset TV source retry flag
                    hideLoadingIndicator(); // Hide loading indicator on success
                    updateTitle(); // Update title to show current source info
                    applyMute();
                    mp.start();
                }
            });
            player.setOnErrorListener(new IjkMediaPlayer.OnErrorListener() {
                @Override
                public boolean onError(IMediaPlayer mp, int what, int extra) {
                    return handlePlaybackError();
                }
            });
            player.prepareAsync();
        } catch (IOException e) {
            handlePlaybackError();
        }
    }

    private String getCurrentUrl() {
        if (camera instanceof TvChannel) {
            return ((TvChannel) camera).getCurrentSource();
        } else {
            return camera.url;
        }
    }

    private boolean handlePlaybackError() {
        // First try different sources for TV channels
        if (camera instanceof TvChannel && !isRetryingTvSources) {
            TvChannel tvChannel = (TvChannel) camera;
            if (tvChannel.hasNextSource()) {
                isRetryingTvSources = true;
                tvChannel.getNextSource(); // Switch to next source
                focusHandler.postDelayed(() -> {
                    if (camera != null) {
                        prepareAndStart();
                    }
                }, 1000); // Shorter delay for source switching
                return true;
            }
        }

        // If no more sources or not a TV channel, try regular retry
        if (retryCount < MAX_RETRY_COUNT) {
            retryCount++;
            focusHandler.postDelayed(() -> {
                if (camera != null) {
                    prepareAndStart();
                }
            }, RETRY_DELAY_MS);
        } else {
            // All retries exhausted, reset for next attempt
            retryCount = 0;
            isRetryingTvSources = false;
            if (camera instanceof TvChannel) {
                ((TvChannel) camera).resetSourceIndex();
            }
        }
        return true; // Always handle the error
    }

    private void startFocusTimeout() {
        cancelFocusTimeout();
        focusTimeoutRunnable = () -> {
            if (hasFocus()) {
                clearFocus();
            }
        };
        focusHandler.postDelayed(focusTimeoutRunnable, 3000); // 3 seconds
    }

    private void cancelFocusTimeout() {
        if (focusTimeoutRunnable != null) {
            focusHandler.removeCallbacks(focusTimeoutRunnable);
            focusTimeoutRunnable = null;
        }
    }

    private void showLoadingIndicator() {
        if (loadingIndicator != null) {
            loadingIndicator.setVisibility(VISIBLE);
        }
    }

    private void hideLoadingIndicator() {
        if (loadingIndicator != null) {
            loadingIndicator.setVisibility(GONE);
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        // Reset focus timeout on any key press
        if (hasFocus()) {
            startFocusTimeout();
        }

        // Handle specific keys if needed
        switch (keyCode) {
            case KeyEvent.KEYCODE_DPAD_CENTER:
            case KeyEvent.KEYCODE_ENTER:
            case KeyEvent.KEYCODE_BUTTON_A:
                // Let parent handle this (MainActivity will show menu)
                return super.onKeyDown(keyCode, event);
            case KeyEvent.KEYCODE_DPAD_LEFT:
            case KeyEvent.KEYCODE_DPAD_RIGHT:
            case KeyEvent.KEYCODE_DPAD_UP:
            case KeyEvent.KEYCODE_DPAD_DOWN:
                // Let the system handle directional navigation
                return super.onKeyDown(keyCode, event);
            default:
                return super.onKeyDown(keyCode, event);
        }
    }

    public void release() {
        cancelFocusTimeout();
        hideLoadingIndicator(); // Hide loading indicator when releasing
        if (player != null) {
            try { player.stop(); } catch (Exception ignored) {}
            player.reset();
            player.release();
            player = null;
        }
    }

    @Override
    public void surfaceCreated(SurfaceHolder holder) {
        restartIfReady();
    }

    @Override
    public void surfaceChanged(SurfaceHolder holder, int format, int width, int height) { }

    @Override
    public void surfaceDestroyed(SurfaceHolder holder) {
        release();
    }
}
